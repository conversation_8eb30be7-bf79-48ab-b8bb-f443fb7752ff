
function unlock_content() {
    console.log("Unlocking content...")
    videos = document.getElementsByClassName("bbMediaWrapper-inner");


    [...videos].forEach(video => {
        poster = video.getElementsByClassName("video-easter-egg-poster")
        if (video.getElementsByTagName("video").length > 0) {
            console.log("Video tag found, removing blocking overlay");

            video.getElementsByClassName("video-easter-egg-poster")[0]?.remove();
            video.getElementsByClassName("video-easter-egg-blocker")[0]?.remove();
            video.getElementsByClassName("video-easter-egg-overlay")[0]?.remove();

        } else if (poster.length > 0) {
            console.log("No video found, creating video tag from poster...");
            imgSrc = poster[0].getElementsByTagName("img")[0].src;

            // Generic function to convert poster/thumbnail URLs to video URLs
            function getVideoUrls(posterUrl) {
                let baseUrl = posterUrl;

                // Pattern 1: /data/attachments/posters/ -> /data/video/
                if (baseUrl.includes("/data/attachments/posters/")) {
                    baseUrl = baseUrl.replace("/data/attachments/posters/", "/data/video/");
                }
                // Pattern 2: /data/lsvideo/thumbnails/ -> /data/lsvideo/videos/
                else if (baseUrl.includes("/thumbnails/")) {
                    baseUrl = baseUrl.replace("/thumbnails/", "/videos/");
                }

                baseUrl = baseUrl.replace(/\.(jpg|jpeg|png|webp)$/i, "");

                return [
                    baseUrl + ".mp4",
                    baseUrl + ".mov",
                    baseUrl + ".webm"
                ];
            }


            // Approach 1: Simple - Let HTML5 try multiple formats
            const videoUrls = getVideoUrls(imgSrc);
            const sourceTags = videoUrls.map(url => `<source src="${url}" />`).join('\n        ');

            console.log('Trying video URLs:', videoUrls);
            video.innerHTML = `<video controls controlsList="nodownload" preload="metadata" class="lazy-video" data-poster="${imgSrc}">
        ${sourceTags}
        <div class="bbMediaWrapper-fallback">Your browser is not able to display this video.</div>
        </video>`;

        }
    })
}

unlock_content();
