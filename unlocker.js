
function unlock_content() {
    console.log("Unlocking content...")
    videos = document.getElementsByClassName("bbMediaWrapper-inner");


    [...videos].forEach(video => {
        poster = video.getElementsByClassName("video-easter-egg-poster")
        if (poster.length > 0) {
            console.log("Poster found");
            imgSrc = poster[0].getElementsByTagName("img")[0].src;

            // Generic function to convert poster/thumbnail URLs to video URLs
            function getVideoUrl(posterUrl) {
                let videoUrl = posterUrl;

                // Pattern 1: /data/attachments/posters/ -> /data/video/
                if (videoUrl.includes("/data/attachments/posters/")) {
                    videoUrl = videoUrl.replace("/data/attachments/posters/", "/data/video/");
                }

                
                // Pattern 2: /data/lsvideo/thumbnails/ -> /data/lsvideo/videos/
                else if (videoUrl.includes("/data/lsvideo/thumbnails/")) {
                    videoUrl = videoUrl.replace("/data/lsvideo/thumbnails/", "/data/lsvideo/videos/");
                }
                // Add more patterns here as needed

                // Convert image extension to video extension
                videoUrl = videoUrl.replace(/\.(jpg|jpeg|png|webp)$/i, ".mp4");

                return videoUrl;
            }


            videoSrc = getVideoUrl(imgSrc);

            console.log(videoSrc);
            video.innerHTML = `<video controls controlsList="nodownload" preload="metadata" class="lazy-video" data-poster="${imgSrc}">
        <source src="${videoSrc}" />
        <div class="bbMediaWrapper-fallback">Your browser is not able to display this video.</div>
        </video>`;

        } else if (video.getElementsByTagName("video").length > 0) {
            console.log("No poster found,");

            // remove ALL video-easter-egg-poster, class="video-easter-egg-blocker" and video-easter-egg-overlay elements inside video
            video.getElementsByClassName("video-easter-egg-poster")[0]?.remove();
            video.getElementsByClassName("video-easter-egg-blocker")[0]?.remove();
            video.getElementsByClassName("video-easter-egg-overlay")[0]?.remove();

        }
    })
}


unlock_content();
console.log("ran first time");
setTimeout(unlock_content, 1000); 